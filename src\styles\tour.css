/* Tour highlighting styles */
.tour-highlight {
  position: relative;
  z-index: 9997 !important;
  box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.6), 0 0 20px rgba(25, 118, 210, 0.4) !important;
  border-radius: 4px !important;
  animation: tour-pulse 2s infinite;
  background-color: rgba(25, 118, 210, 0.05) !important;
}

@keyframes tour-pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.6), 0 0 20px rgba(25, 118, 210, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(25, 118, 210, 0.4), 0 0 30px rgba(25, 118, 210, 0.6);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.6), 0 0 20px rgba(25, 118, 210, 0.4);
  }
}

/* Tour overlay styles */
.tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  pointer-events: none;
}

/* Tour tooltip styles */
.tour-tooltip {
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Tour step indicator */
.tour-step-indicator {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.tour-step-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transition: background-color 0.3s ease;
}

.tour-step-dot.active {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Responsive tour styles */
@media (max-width: 768px) {
  .tour-tooltip {
    bottom: 10px !important;
    left: 10px !important;
    right: 10px !important;
    transform: none !important;
    width: auto !important;
  }
}

/* Tour navigation button styles */
.tour-nav-button {
  transition: all 0.3s ease;
}

.tour-nav-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Tour welcome step styles */
.tour-welcome {
  text-align: center;
  padding: 24px;
}

.tour-welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #1976d2;
}
