import React from 'react';
import { Box, CssBaseline, Container } from '@mui/material';
import TopNavigation from './TopNavigation';
import TourTooltip from '../tour/TourTooltip';
import Footer from './Footer';

const AppLayout = ({ children }) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <CssBaseline />
      <TopNavigation />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: 'background.default',
          mt: 0,
          overflow: 'auto'
        }}
      >
        <Container maxWidth="xl" sx={{ py: 2 }}>
          {children}
        </Container>
      </Box>

      {/* Global Tour Tooltip */}
      <TourTooltip />
      
      {/* Footer */}
      <Footer />
    </Box>
  );
};

export default AppLayout;