import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Tabs,
  Tab,
  Grid,
  Paper,
  Button,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { motion } from 'framer-motion';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import ReceiptIcon from '@mui/icons-material/Receipt';
import BarChartIcon from '@mui/icons-material/BarChart';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import InfoIcon from '@mui/icons-material/Info';
import TourIcon from '@mui/icons-material/Tour';
import { useTour } from '../contexts/TourContext';

// Placeholder images - in a real implementation, these would be actual screenshots
const PLACEHOLDER_IMAGES = {
  dashboard: "https://via.placeholder.com/800x450?text=Dashboard+Screenshot",
  salary: "https://via.placeholder.com/800x450?text=Salary+Module+Screenshot",
  tally: "https://via.placeholder.com/800x450?text=Tally+Module+Screenshot",
  costCenter: "https://via.placeholder.com/800x450?text=Cost+Center+Screenshot",
  resourceUtilization: "https://via.placeholder.com/800x450?text=Resource+Utilization+Screenshot",
  resourceCost: "https://via.placeholder.com/800x450?text=Resource+Cost+Screenshot",
  employeeDiscrepancy: "https://via.placeholder.com/800x450?text=Employee+Discrepancy+Screenshot",
  workflow: "https://via.placeholder.com/800x450?text=Workflow+Diagram",
  dataRelationships: "https://via.placeholder.com/800x450?text=Data+Relationships+Diagram"
};

const AboutApp = () => {
  const [tabValue, setTabValue] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { startTour, tourSteps } = useTour();

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };



  const renderUserGuide = () => (
    <Box>
      <Typography variant="h6" gutterBottom>User Guide</Typography>
      <Divider sx={{ mb: 3 }} />
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <DashboardIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Dashboard</Typography>
              </Box>
              <Typography variant="body2" paragraph>
                The Dashboard provides a comprehensive overview of your financial data with key metrics and visualizations:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="View monthly financial summaries and trends" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Monitor cost center performance with interactive charts" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Track resource allocation and utilization metrics" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Access quick links to detailed reports and analysis" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PeopleIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Salary</Typography>
              </Box>
              <Typography variant="body2" paragraph>
                The Salary module allows you to manage employee compensation data:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Upload salary data using Excel templates" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="View and filter employee salary information" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Generate salary reports by department or cost center" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Track historical salary data and changes over time" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ReceiptIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Tally</Typography>
              </Box>
              <Typography variant="body2" paragraph>
                The Tally module generates accounting reports compatible with Tally ERP:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Select month to generate Tally-compatible reports" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Configure cost center mappings for accurate reporting" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Export data in Tally-compatible format" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="View aggregated data and summary reports" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <BarChartIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Cost Center</Typography>
              </Box>
              <Typography variant="body2" paragraph>
                The Cost Center module helps manage organizational cost structures:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Upload and manage cost center data" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="View cost breakdowns by department and project" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Track budget allocation and utilization" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Generate cost center reports for financial analysis" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <BarChartIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Resource Utilization</Typography>
              </Box>
              <Typography variant="body2" paragraph>
                The Resource Utilization module tracks how resources are being used:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Monitor resource allocation across projects" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Analyze utilization rates and efficiency metrics" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Identify underutilized or overallocated resources" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Generate utilization reports for management review" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <BarChartIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Resource Cost</Typography>
              </Box>
              <Typography variant="body2" paragraph>
                The Resource Cost module analyzes the financial impact of resource allocation:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Calculate resource costs across projects and departments" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Compare actual costs against budgeted amounts" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Identify cost-saving opportunities" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Generate cost analysis reports for financial planning" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PeopleIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Employee Discrepancy</Typography>
              </Box>
              <Typography variant="body2" paragraph>
                The Employee Discrepancy module identifies inconsistencies in employee data:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Compare employee data across different systems" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Identify discrepancies in salary, position, or department" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Generate reconciliation reports for HR and Finance" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 36 }}>•</ListItemIcon>
                  <ListItemText primary="Track resolution of identified discrepancies" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderVisualAids = () => (
    <Box>
      <Typography variant="h6" gutterBottom>Visual Aids</Typography>
      <Divider sx={{ mb: 3 }} />
      
      <Typography variant="subtitle1" gutterBottom>Module Screenshots</Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" gutterBottom>Dashboard</Typography>
              <Box 
                component="img" 
                src={PLACEHOLDER_IMAGES.dashboard} 
                alt="Dashboard Screenshot" 
                sx={{ width: '100%', borderRadius: 1 }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                The Dashboard provides an overview of key financial metrics and trends.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" gutterBottom>Salary Module</Typography>
              <Box 
                component="img" 
                src={PLACEHOLDER_IMAGES.salary} 
                alt="Salary Module Screenshot" 
                sx={{ width: '100%', borderRadius: 1 }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                The Salary module allows you to manage employee compensation data.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Typography variant="subtitle1" gutterBottom>Workflow Diagrams</Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" gutterBottom>Financial Reporting Workflow</Typography>
              <Box 
                component="img" 
                src={PLACEHOLDER_IMAGES.workflow} 
                alt="Workflow Diagram" 
                sx={{ width: '100%', borderRadius: 1 }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                This diagram illustrates the typical workflow for financial reporting in the application.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Typography variant="subtitle1" gutterBottom>Data Relationship Diagrams</Typography>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" gutterBottom>Module Data Relationships</Typography>
              <Box 
                component="img" 
                src={PLACEHOLDER_IMAGES.dataRelationships} 
                alt="Data Relationships Diagram" 
                sx={{ width: '100%', borderRadius: 1 }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                This diagram shows how data flows between different modules in the application.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderAppTour = () => (
    <Box>
      <Typography variant="h6" gutterBottom>Interactive App Tour</Typography>
      <Divider sx={{ mb: 3 }} />
      
      <Paper elevation={3} sx={{ p: 3, mb: 4, backgroundColor: theme.palette.primary.light, color: theme.palette.primary.contrastText }}>
        <Typography variant="h6" gutterBottom>Experience the App Tour</Typography>
        <Typography variant="body1" paragraph>
          Take an interactive tour of the FinReport application to learn about its key features and functionality.
        </Typography>
        <Button 
          variant="contained" 
          color="secondary" 
          startIcon={<TourIcon />}
          onClick={startTour}
        >
          Start Tour
        </Button>
      </Paper>
      
      <Typography variant="subtitle1" gutterBottom>What You'll Learn</Typography>
      <Grid container spacing={2}>
        {tourSteps.map((step, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" alignItems="center" mb={1}>
                  <Typography variant="h6" component="div" sx={{ mr: 1 }}>
                    {index + 1}
                  </Typography>
                  <Typography variant="subtitle1">{step.title}</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {step.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );



  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Container sx={{ padding: { xs: 1, sm: 2, md: 3 } }}>
        <Card sx={{ mb: 3, overflow: 'visible' }} id="welcome">
          <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 1 }}>
              <Typography variant="h5" component="h1" gutterBottom sx={{ mb: 0 }}>
                About FinReport
              </Typography>
              <Button
                variant="contained"
                startIcon={<TourIcon />}
                onClick={startTour}
                size="small"
              >
                Start Tour
              </Button>
            </Box>
            
            <Typography variant="body1" paragraph>
              FinReport is a comprehensive financial reporting application designed to streamline salary management, 
              cost center analysis, resource utilization tracking, and financial data integration with Tally ERP.
            </Typography>
          </CardContent>
        </Card>
        
        <Box sx={{ width: '100%', mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              aria-label="about app tabs"
              variant={isMobile ? "scrollable" : "standard"}
              scrollButtons={isMobile ? "auto" : false}
            >
              <Tab icon={<HelpOutlineIcon />} label="User Guide" id="tab-0" />
              <Tab icon={<InfoIcon />} label="Visual Aids" id="tab-1" />
              <Tab icon={<TourIcon />} label="App Tour" id="tab-2" />
            </Tabs>
          </Box>
          <Box sx={{ p: { xs: 1, sm: 2 }, mt: 2 }}>
            {tabValue === 0 && renderUserGuide()}
            {tabValue === 1 && renderVisualAids()}
            {tabValue === 2 && renderAppTour()}
          </Box>
        </Box>
      </Container>
    </motion.div>
  );
};

export default AboutApp;