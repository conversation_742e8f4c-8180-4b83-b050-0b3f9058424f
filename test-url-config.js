#!/usr/bin/env node

/**
 * Test script to verify URL configuration
 */

const fs = require('fs');
const path = require('path');

function readEnvValue(filePath, key) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const regex = new RegExp(`^${key}=(.*)$`, 'm');
    const match = content.match(regex);
    return match ? match[1] : null;
  } catch (error) {
    return null;
  }
}

function readJsonValue(filePath, key) {
  try {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    return content[key];
  } catch (error) {
    return null;
  }
}

function testConfiguration() {
  console.log('🔍 Testing URL Configuration...\n');

  // Test frontend .env
  const frontendBasePath = readEnvValue('.env', 'REACT_APP_BASE_PATH');
  console.log(`Frontend Base Path (.env): ${frontendBasePath || '(not set)'}`);

  // Test server .env
  const serverBasePath = readEnvValue('server/.env', 'BASE_PATH');
  console.log(`Server Base Path (server/.env): ${serverBasePath || '(not set)'}`);

  // Test package.json
  const homepage = readJsonValue('package.json', 'homepage');
  console.log(`Package.json Homepage: ${homepage || '(not set)'}`);

  // Test manifest.json
  const startUrl = readJsonValue('public/manifest.json', 'start_url');
  console.log(`Manifest Start URL: ${startUrl || '(not set)'}`);

  // Check consistency
  console.log('\n📊 Configuration Analysis:');
  
  const configs = [frontendBasePath, serverBasePath];
  const buildConfigs = [homepage === '.' ? '' : homepage, startUrl === '.' ? '' : startUrl];
  
  const allMatch = configs.every(config => config === frontendBasePath) && 
                   buildConfigs.every(config => config === frontendBasePath);

  if (allMatch) {
    console.log('✅ All configurations are consistent');
    console.log(`🌐 Application will run on base path: ${frontendBasePath || '(root)'}`);
    
    if (frontendBasePath) {
      console.log(`\n📍 URLs:`);
      console.log(`   Development: http://localhost:3000${frontendBasePath}`);
      console.log(`   Production: http://localhost:5000${frontendBasePath}`);
    } else {
      console.log(`\n📍 URLs:`);
      console.log(`   Development: http://localhost:3000/`);
      console.log(`   Production: http://localhost:5000/`);
    }
  } else {
    console.log('⚠️  Configuration mismatch detected:');
    console.log(`   Frontend: ${frontendBasePath}`);
    console.log(`   Server: ${serverBasePath}`);
    console.log(`   Homepage: ${homepage}`);
    console.log(`   Start URL: ${startUrl}`);
    console.log('\n💡 Run: node configure-url-path.js <path> to fix');
  }

  // Test App.js configuration
  try {
    const appContent = fs.readFileSync('src/App.js', 'utf8');
    if (appContent.includes('basename={basePath}')) {
      console.log('✅ React Router basename configuration found');
    } else {
      console.log('❌ React Router basename configuration missing');
    }
  } catch (error) {
    console.log('❌ Could not verify App.js configuration');
  }

  // Test server.js configuration
  try {
    const serverContent = fs.readFileSync('server/server.js', 'utf8');
    if (serverContent.includes('process.env.BASE_PATH')) {
      console.log('✅ Server base path configuration found');
    } else {
      console.log('❌ Server base path configuration missing');
    }
  } catch (error) {
    console.log('❌ Could not verify server.js configuration');
  }

  console.log('\n📖 For detailed instructions, see: CUSTOM_URL_CONFIGURATION.md');
}

if (require.main === module) {
  testConfiguration();
}

module.exports = { testConfiguration };
