#!/usr/bin/env node

/**
 * URL Path Configuration Script
 * 
 * This script helps configure the Financial Report application
 * to run on different base URL paths.
 * 
 * Usage:
 *   node configure-url-path.js /finreport
 *   node configure-url-path.js /finance
 *   node configure-url-path.js root
 */

const fs = require('fs');
const path = require('path');

function updateEnvFile(filePath, key, value) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const regex = new RegExp(`^${key}=.*$`, 'm');
    
    if (regex.test(content)) {
      content = content.replace(regex, `${key}=${value}`);
    } else {
      content += `\n${key}=${value}\n`;
    }
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated ${filePath}: ${key}=${value}`);
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
  }
}

function updatePackageJson(basePath) {
  try {
    const packagePath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    packageJson.homepage = basePath === '' ? '.' : basePath;
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    console.log(`✅ Updated package.json: homepage=${packageJson.homepage}`);
  } catch (error) {
    console.error('❌ Error updating package.json:', error.message);
  }
}

function updateManifest(basePath) {
  try {
    const manifestPath = path.join(__dirname, 'public', 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    manifest.start_url = basePath === '' ? '.' : basePath;
    
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
    console.log(`✅ Updated manifest.json: start_url=${manifest.start_url}`);
  } catch (error) {
    console.error('❌ Error updating manifest.json:', error.message);
  }
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
📋 URL Path Configuration Script

Usage:
  node configure-url-path.js <path>

Examples:
  node configure-url-path.js /finreport    # Set base path to /finreport
  node configure-url-path.js /finance      # Set base path to /finance
  node configure-url-path.js root          # Set to root path (no base path)

Current configuration:
  Frontend: ${process.env.REACT_APP_BASE_PATH || 'not set'}
  Server: ${process.env.BASE_PATH || 'not set'}
    `);
    return;
  }

  let basePath = args[0];
  
  // Handle special case for root path
  if (basePath === 'root' || basePath === '/') {
    basePath = '';
  }
  
  // Ensure path starts with / if not empty
  if (basePath && !basePath.startsWith('/')) {
    basePath = '/' + basePath;
  }

  console.log(`🔧 Configuring application for base path: ${basePath || '(root)'}`);
  
  // Update frontend .env
  updateEnvFile('.env', 'REACT_APP_BASE_PATH', basePath);
  
  // Update server .env
  updateEnvFile('server/.env', 'BASE_PATH', basePath);
  
  // Update package.json
  updatePackageJson(basePath);
  
  // Update manifest.json
  updateManifest(basePath);
  
  console.log(`
✅ Configuration complete!

Next steps:
1. For development:
   npm start
   
2. For production:
   npm run build
   cd server && npm start

3. Access your application at:
   Development: http://localhost:3000${basePath}
   Production: http://localhost:5000${basePath}

📖 See CUSTOM_URL_CONFIGURATION.md for detailed instructions.
  `);
}

if (require.main === module) {
  main();
}

module.exports = { updateEnvFile, updatePackageJson, updateManifest };
