{"name": "finreport", "version": "0.1.0", "private": true, "homepage": "/finreport", "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.14.20", "@mui/system": "^5.14.20", "@mui/utils": "^5.14.20", "@mui/x-date-pickers": "^6.18.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "chart.js": "^4.4.1", "date-fns": "^2.30.0", "framer-motion": "^10.18.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.3", "react-scripts": "5.0.1", "recharts": "^2.10.4", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "devDependencies": {"concurrently": "^8.2.2"}, "scripts": {"start": "react-scripts start", "start:dev": "concurrently \"npm run server\" \"npm run client\"", "client": "react-scripts start", "server": "cd server && npm start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}