@echo off
REM ============================================================================
REM Financial Report Application - Production Restart Script
REM ============================================================================
REM This batch file quickly restarts the production server without rebuilding
REM Use this when you only need to restart the server (no frontend changes)
REM ============================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Financial Report - Production Restart
color 0B

echo.
echo ============================================================================
echo                    FINANCIAL REPORT APPLICATION
echo                       Production Restart Script
echo ============================================================================
echo.

REM Check if we're in the correct directory
if not exist "package.json" (
    echo [ERROR] package.json not found!
    echo Please run this script from the Financial Report application root directory.
    echo.
    pause
    exit /b 1
)

REM Check if build directory exists
if not exist "build" (
    echo [ERROR] Build directory not found!
    echo Please run 'start-production.bat' first to build the application.
    echo.
    pause
    exit /b 1
)

REM Set production environment
set NODE_ENV=production
echo [INFO] Environment: %NODE_ENV%

REM Navigate to server directory
echo [INFO] Navigating to server directory...
cd server

REM Read configuration
set BASE_PATH=/finreport
set PORT=5000
if exist ".env" (
    for /f "tokens=2 delims==" %%a in ('findstr "BASE_PATH" .env 2^>nul') do set BASE_PATH=%%a
    for /f "tokens=2 delims==" %%a in ('findstr "PORT" .env 2^>nul') do set PORT=%%a
)

echo [INFO] Configuration:
echo       Port: %PORT%
echo       Base Path: %BASE_PATH%
echo.
echo [READY] Application URL: http://localhost:%PORT%%BASE_PATH%
echo.
echo [INFO] Starting server... (Press Ctrl+C to stop)
echo.

REM Start the server
npm start

REM Return to original directory when server stops
cd ..
echo.
echo [INFO] Server stopped. Returned to application root directory.
pause
