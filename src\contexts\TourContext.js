import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const TourContext = createContext();

export const useTour = () => {
  const context = useContext(TourContext);
  if (!context) {
    throw new Error('useTour must be used within a TourProvider');
  }
  return context;
};

export const TourProvider = ({ children }) => {
  const [tourActive, setTourActive] = useState(false);
  const [tourStep, setTourStep] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();

  const tourSteps = [
    {
      title: "Welcome to FinReport",
      description: "This tour will guide you through the key features of the application. Click 'Next' to start exploring!",
      target: "welcome",
      placement: "center",
      route: "/about"
    },
    {
      title: "Dashboard",
      description: "The Dashboard provides an overview of key financial metrics and trends.",
      target: "nav-dashboard",
      placement: "bottom",
      route: "/",
      action: () => navigate('/')
    },
    {
      title: "Salary Module",
      description: "Upload and manage salary data for your organization.",
      target: "nav-salary",
      placement: "bottom",
      route: "/salary",
      action: () => navigate('/salary')
    },
    {
      title: "Tally Module",
      description: "Generate Tally-compatible reports from your financial data.",
      target: "nav-tally",
      placement: "bottom",
      route: "/tally",
      action: () => navigate('/tally')
    },
    {
      title: "Cost Center",
      description: "Manage and analyze cost centers across your organization.",
      target: "nav-cost-center",
      placement: "bottom",
      route: "/cost-center",
      action: () => navigate('/cost-center')
    },
    {
      title: "Resource Utilization",
      description: "Track how resources are being utilized across projects.",
      target: "nav-resource-utilization",
      placement: "bottom",
      route: "/resource-utilization",
      action: () => navigate('/resource-utilization')
    }
  ];

  const startTour = () => {
    setTourActive(true);
    setTourStep(0);
    // Navigate to about page to start the tour
    if (location.pathname !== '/about') {
      navigate('/about');
    }
  };

  const endTour = () => {
    setTourActive(false);
    setTourStep(0);
  };

  const nextTourStep = () => {
    if (tourStep < tourSteps.length - 1) {
      setTourStep(tourStep + 1);
    } else {
      endTour();
    }
  };

  const previousTourStep = () => {
    if (tourStep > 0) {
      setTourStep(tourStep - 1);
    }
  };

  const goToTourStep = (step) => {
    if (step >= 0 && step < tourSteps.length) {
      setTourStep(step);
    }
  };

  // Execute the action for the current tour step with proper timing
  useEffect(() => {
    if (tourActive && tourSteps[tourStep]?.action) {
      // Add a delay to ensure smooth navigation
      const timer = setTimeout(() => {
        tourSteps[tourStep].action();
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [tourStep, tourActive, navigate]);

  // Add visual highlighting for tour targets
  useEffect(() => {
    if (tourActive && tourSteps[tourStep]?.target) {
      const targetElement = document.getElementById(tourSteps[tourStep].target);
      if (targetElement) {
        // Add highlighting class
        targetElement.classList.add('tour-highlight');
        
        // Scroll element into view
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center'
        });
        
        // Remove highlighting when tour step changes or ends
        return () => {
          targetElement.classList.remove('tour-highlight');
        };
      }
    }
  }, [tourStep, tourActive]);

  const value = {
    tourActive,
    tourStep,
    tourSteps,
    startTour,
    endTour,
    nextTourStep,
    previousTourStep,
    goToTourStep,
    currentStep: tourSteps[tourStep]
  };

  return (
    <TourContext.Provider value={value}>
      {children}
    </TourContext.Provider>
  );
};
