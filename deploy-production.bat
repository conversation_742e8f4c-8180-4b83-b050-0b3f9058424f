@echo off
REM ============================================================================
REM Financial Report Application - Advanced Production Deployment Script
REM ============================================================================
REM This comprehensive deployment script includes:
REM - Interactive deployment options
REM - Health checks and validation
REM - Log management
REM - Backup creation
REM - Environment validation
REM - Network accessibility checks
REM ============================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Financial Report - Advanced Production Deployment
color 0E

:MAIN_MENU
cls
echo.
echo ============================================================================
echo                    FINANCIAL REPORT APPLICATION
echo                   Advanced Production Deployment
echo ============================================================================
echo.
echo Please select a deployment option:
echo.
echo [1] Full Deployment (Build + Start Server)
echo [2] Quick Start (Start Server Only)
echo [3] Build Only (No Server Start)
echo [4] Health Check
echo [5] View Configuration
echo [6] Clean Build (Remove build folder and rebuild)
echo [7] Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto FULL_DEPLOY
if "%choice%"=="2" goto QUICK_START
if "%choice%"=="3" goto BUILD_ONLY
if "%choice%"=="4" goto HEALTH_CHECK
if "%choice%"=="5" goto VIEW_CONFIG
if "%choice%"=="6" goto CLEAN_BUILD
if "%choice%"=="7" goto EXIT
goto MAIN_MENU

:FULL_DEPLOY
cls
echo.
echo ============================================================================
echo                          FULL DEPLOYMENT
echo ============================================================================
echo.

REM Validate environment
call :VALIDATE_ENVIRONMENT
if errorlevel 1 goto MAIN_MENU

REM Create backup if build exists
if exist "build" (
    echo [INFO] Creating backup of existing build...
    if exist "build_backup" rmdir /s /q "build_backup"
    move "build" "build_backup" >nul 2>&1
    echo [SUCCESS] Backup created as 'build_backup'
    echo.
)

REM Build frontend
call :BUILD_FRONTEND
if errorlevel 1 goto MAIN_MENU

REM Start server
call :START_SERVER
goto MAIN_MENU

:QUICK_START
cls
echo.
echo ============================================================================
echo                           QUICK START
echo ============================================================================
echo.

REM Validate environment
call :VALIDATE_ENVIRONMENT
if errorlevel 1 goto MAIN_MENU

REM Check if build exists
if not exist "build" (
    echo [ERROR] Build directory not found!
    echo Please run Full Deployment first to build the application.
    echo.
    pause
    goto MAIN_MENU
)

REM Start server
call :START_SERVER
goto MAIN_MENU

:BUILD_ONLY
cls
echo.
echo ============================================================================
echo                            BUILD ONLY
echo ============================================================================
echo.

REM Validate environment
call :VALIDATE_ENVIRONMENT
if errorlevel 1 goto MAIN_MENU

REM Build frontend
call :BUILD_FRONTEND
if errorlevel 1 goto MAIN_MENU

echo [SUCCESS] Build completed successfully!
echo [INFO] Use Quick Start to run the server.
echo.
pause
goto MAIN_MENU

:CLEAN_BUILD
cls
echo.
echo ============================================================================
echo                           CLEAN BUILD
echo ============================================================================
echo.

if exist "build" (
    echo [INFO] Removing existing build directory...
    rmdir /s /q "build"
    echo [SUCCESS] Build directory removed
    echo.
)

if exist "build_backup" (
    echo [INFO] Removing build backup...
    rmdir /s /q "build_backup"
    echo [SUCCESS] Build backup removed
    echo.
)

REM Validate environment
call :VALIDATE_ENVIRONMENT
if errorlevel 1 goto MAIN_MENU

REM Build frontend
call :BUILD_FRONTEND
if errorlevel 1 goto MAIN_MENU

echo [SUCCESS] Clean build completed successfully!
echo.
pause
goto MAIN_MENU

:HEALTH_CHECK
cls
echo.
echo ============================================================================
echo                          HEALTH CHECK
echo ============================================================================
echo.

call :VALIDATE_ENVIRONMENT
call :DISPLAY_CONFIG

echo [INFO] Running configuration test...
if exist "test-url-config.js" (
    node test-url-config.js
) else (
    echo [WARNING] test-url-config.js not found - skipping configuration test
)

echo.
echo [INFO] Health check completed
pause
goto MAIN_MENU

:VIEW_CONFIG
cls
echo.
echo ============================================================================
echo                         CONFIGURATION
echo ============================================================================
echo.

call :DISPLAY_CONFIG
pause
goto MAIN_MENU

REM ============================================================================
REM SUBROUTINES
REM ============================================================================

:VALIDATE_ENVIRONMENT
echo [INFO] Validating environment...

REM Check if we're in the correct directory
if not exist "package.json" (
    echo [ERROR] package.json not found!
    echo Please run this script from the Financial Report application root directory.
    echo.
    pause
    exit /b 1
)

REM Check Node.js installation
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH!
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Check npm installation
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed or not in PATH!
    echo Please install npm or reinstall Node.js.
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Environment validation passed
echo.
exit /b 0

:BUILD_FRONTEND
echo [INFO] Building React frontend application...
echo [INFO] This may take a few minutes...
echo.

set NODE_ENV=production
npm run build

if errorlevel 1 (
    echo.
    echo [ERROR] Frontend build failed!
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Frontend build completed successfully!
echo.
exit /b 0

:START_SERVER
echo [INFO] Preparing to start server...

REM Navigate to server directory
cd server

REM Check server dependencies
if not exist "node_modules" (
    echo [INFO] Installing server dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install server dependencies!
        pause
        cd ..
        exit /b 1
    )
)

call :DISPLAY_CONFIG

echo.
echo [INFO] Starting server... (Press Ctrl+C to stop)
echo.
echo ============================================================================
echo                            SERVER OUTPUT
echo ============================================================================

set NODE_ENV=production
npm start

REM Return to original directory when server stops
cd ..
echo.
echo [INFO] Server stopped. Returned to application root directory.
pause
exit /b 0

:DISPLAY_CONFIG
REM Read configuration from files
set BASE_PATH=/finreport
set PORT=5000

if exist "server\.env" (
    for /f "tokens=2 delims==" %%a in ('findstr "BASE_PATH" server\.env 2^>nul') do set BASE_PATH=%%a
    for /f "tokens=2 delims==" %%a in ('findstr "PORT" server\.env 2^>nul') do set PORT=%%a
)

echo [INFO] Current Configuration:
echo       Environment: production
echo       Port: %PORT%
echo       Base Path: %BASE_PATH%
echo       Application URL: http://localhost:%PORT%%BASE_PATH%
echo       Build Directory: build\
echo       Server Directory: server\
echo.
exit /b 0

:EXIT
echo.
echo [INFO] Exiting deployment script...
echo Thank you for using Financial Report Application!
echo.
pause
exit /b 0
