@echo off
REM ============================================================================
REM Financial Report Application - Master Management Script
REM ============================================================================
REM This is the main entry point for all Financial Report deployment operations
REM Provides easy access to all deployment, management, and utility scripts
REM ============================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Financial Report - Application Manager
color 0F

:MAIN_MENU
cls
echo.
echo ============================================================================
echo                    FINANCIAL REPORT APPLICATION
echo                         Management Console
echo ============================================================================
echo.
echo Welcome to the Financial Report Application Management Console!
echo This tool provides easy access to all deployment and management operations.
echo.
echo ============================================================================
echo                           MAIN MENU
echo ============================================================================
echo.
echo DEPLOYMENT OPTIONS:
echo [1] Start Production (Full Build + Server Start)
echo [2] Restart Production (Server Only - No Build)
echo [3] Advanced Deployment (Interactive Options)
echo [4] Stop Production Server
echo.
echo DEVELOPMENT OPTIONS:
echo [5] Start Development Server
echo [6] Start Development with Backend
echo.
echo CONFIGURATION OPTIONS:
echo [7] Configure URL Path
echo [8] Test Configuration
echo [9] View Current Configuration
echo.
echo UTILITY OPTIONS:
echo [A] Open Application in Browser
echo [B] View Application Logs
echo [C] Clean Build Files
echo [D] Install/Update Dependencies
echo.
echo [H] Help & Documentation
echo [X] Exit
echo.
set /p choice="Enter your choice: "

REM Convert to uppercase for case-insensitive comparison
for %%i in (a b c d h x) do if /i "%choice%"=="%%i" set choice=%%i

if "%choice%"=="1" goto START_PRODUCTION
if "%choice%"=="2" goto RESTART_PRODUCTION
if "%choice%"=="3" goto ADVANCED_DEPLOYMENT
if "%choice%"=="4" goto STOP_PRODUCTION
if "%choice%"=="5" goto START_DEVELOPMENT
if "%choice%"=="6" goto START_DEV_WITH_BACKEND
if "%choice%"=="7" goto CONFIGURE_URL
if "%choice%"=="8" goto TEST_CONFIG
if "%choice%"=="9" goto VIEW_CONFIG
if /i "%choice%"=="a" goto OPEN_BROWSER
if /i "%choice%"=="b" goto VIEW_LOGS
if /i "%choice%"=="c" goto CLEAN_BUILD
if /i "%choice%"=="d" goto UPDATE_DEPS
if /i "%choice%"=="h" goto HELP
if /i "%choice%"=="x" goto EXIT

echo [ERROR] Invalid choice. Please try again.
pause
goto MAIN_MENU

:START_PRODUCTION
cls
echo [INFO] Starting Full Production Deployment...
echo.
if exist "start-production.bat" (
    call start-production.bat
) else (
    echo [ERROR] start-production.bat not found!
    pause
)
goto MAIN_MENU

:RESTART_PRODUCTION
cls
echo [INFO] Restarting Production Server...
echo.
if exist "restart-production.bat" (
    call restart-production.bat
) else (
    echo [ERROR] restart-production.bat not found!
    pause
)
goto MAIN_MENU

:ADVANCED_DEPLOYMENT
cls
echo [INFO] Opening Advanced Deployment Options...
echo.
if exist "deploy-production.bat" (
    call deploy-production.bat
) else (
    echo [ERROR] deploy-production.bat not found!
    pause
)
goto MAIN_MENU

:STOP_PRODUCTION
cls
echo [INFO] Stopping Production Server...
echo.
if exist "stop-production.bat" (
    call stop-production.bat
) else (
    echo [ERROR] stop-production.bat not found!
    pause
)
goto MAIN_MENU

:START_DEVELOPMENT
cls
echo [INFO] Starting Development Server...
echo.
echo [INFO] This will start the React development server on http://localhost:3000
echo [INFO] Press Ctrl+C to stop the server
echo.
npm start
pause
goto MAIN_MENU

:START_DEV_WITH_BACKEND
cls
echo [INFO] Starting Development with Backend...
echo.
if exist "start-dev-server.cmd" (
    call start-dev-server.cmd
) else (
    echo [INFO] Using npm script to start both frontend and backend...
    npm run start:dev
)
pause
goto MAIN_MENU

:CONFIGURE_URL
cls
echo [INFO] URL Path Configuration...
echo.
if exist "configure-url-path.js" (
    echo Current configuration options:
    echo [1] /finreport (default)
    echo [2] /finance
    echo [3] Root path (/)
    echo [4] Custom path
    echo.
    set /p url_choice="Enter choice (1-4): "
    
    if "%url_choice%"=="1" node configure-url-path.js /finreport
    if "%url_choice%"=="2" node configure-url-path.js /finance
    if "%url_choice%"=="3" node configure-url-path.js root
    if "%url_choice%"=="4" (
        set /p custom_path="Enter custom path (e.g., /myapp): "
        node configure-url-path.js !custom_path!
    )
) else (
    echo [ERROR] configure-url-path.js not found!
)
pause
goto MAIN_MENU

:TEST_CONFIG
cls
echo [INFO] Testing Configuration...
echo.
if exist "test-url-config.js" (
    node test-url-config.js
) else (
    echo [ERROR] test-url-config.js not found!
)
pause
goto MAIN_MENU

:VIEW_CONFIG
cls
echo [INFO] Current Configuration:
echo.
call :DISPLAY_CURRENT_CONFIG
pause
goto MAIN_MENU

:OPEN_BROWSER
cls
echo [INFO] Opening Application in Browser...
echo.

REM Read configuration to get the correct URL
set BASE_PATH=/finreport
set PORT=5000
if exist "server\.env" (
    for /f "tokens=2 delims==" %%a in ('findstr "BASE_PATH" server\.env 2^>nul') do set BASE_PATH=%%a
    for /f "tokens=2 delims==" %%a in ('findstr "PORT" server\.env 2^>nul') do set PORT=%%a
)

echo [INFO] Attempting to open: http://localhost:%PORT%%BASE_PATH%
start http://localhost:%PORT%%BASE_PATH%

echo [INFO] If the browser didn't open automatically, navigate to:
echo       http://localhost:%PORT%%BASE_PATH%
pause
goto MAIN_MENU

:VIEW_LOGS
cls
echo [INFO] Application Logs
echo.
echo [INFO] This feature would show application logs.
echo [INFO] Currently, logs are displayed in the server console.
echo [INFO] For production logging, consider implementing file-based logging.
pause
goto MAIN_MENU

:CLEAN_BUILD
cls
echo [INFO] Cleaning Build Files...
echo.
if exist "build" (
    echo [INFO] Removing build directory...
    rmdir /s /q "build"
    echo [SUCCESS] Build directory removed.
) else (
    echo [INFO] No build directory found.
)

if exist "build_backup" (
    echo [INFO] Removing build backup...
    rmdir /s /q "build_backup"
    echo [SUCCESS] Build backup removed.
)

echo [SUCCESS] Clean operation completed.
pause
goto MAIN_MENU

:UPDATE_DEPS
cls
echo [INFO] Installing/Updating Dependencies...
echo.
echo [INFO] Updating frontend dependencies...
npm install

echo.
echo [INFO] Updating server dependencies...
cd server
npm install
cd ..

echo.
echo [SUCCESS] Dependencies updated successfully.
pause
goto MAIN_MENU

:HELP
cls
echo.
echo ============================================================================
echo                              HELP
echo ============================================================================
echo.
echo FINANCIAL REPORT APPLICATION MANAGEMENT CONSOLE
echo.
echo This management console provides easy access to all deployment and
echo management operations for the Financial Report application.
echo.
echo QUICK START:
echo 1. For production deployment: Choose option [1]
echo 2. For development: Choose option [5]
echo 3. To change URL path: Choose option [7]
echo.
echo DEPLOYMENT SCRIPTS:
echo - start-production.bat: Full production deployment
echo - restart-production.bat: Quick server restart
echo - deploy-production.bat: Advanced deployment options
echo - stop-production.bat: Stop production server
echo.
echo CONFIGURATION:
echo - configure-url-path.js: Change application URL path
echo - test-url-config.js: Validate configuration
echo.
echo DOCUMENTATION:
echo - CUSTOM_URL_CONFIGURATION.md: Detailed URL configuration guide
echo - README.md: General application documentation
echo.
echo For more help, see the documentation files in the application directory.
echo.
pause
goto MAIN_MENU

:DISPLAY_CURRENT_CONFIG
REM Read and display current configuration
set BASE_PATH=/finreport
set PORT=5000
set REACT_BASE_PATH=not set

if exist ".env" (
    for /f "tokens=2 delims==" %%a in ('findstr "REACT_APP_BASE_PATH" .env 2^>nul') do set REACT_BASE_PATH=%%a
)

if exist "server\.env" (
    for /f "tokens=2 delims==" %%a in ('findstr "BASE_PATH" server\.env 2^>nul') do set BASE_PATH=%%a
    for /f "tokens=2 delims==" %%a in ('findstr "PORT" server\.env 2^>nul') do set PORT=%%a
)

echo Frontend Base Path: %REACT_BASE_PATH%
echo Server Base Path: %BASE_PATH%
echo Server Port: %PORT%
echo.
echo Application URLs:
echo   Development: http://localhost:3000%REACT_BASE_PATH%
echo   Production:  http://localhost:%PORT%%BASE_PATH%
echo.
exit /b 0

:EXIT
cls
echo.
echo ============================================================================
echo Thank you for using Financial Report Application Management Console!
echo ============================================================================
echo.
echo The application management session has ended.
echo.
echo Quick reference for direct script execution:
echo - start-production.bat: Full production deployment
echo - restart-production.bat: Quick server restart
echo - npm start: Development server
echo.
pause
exit /b 0
