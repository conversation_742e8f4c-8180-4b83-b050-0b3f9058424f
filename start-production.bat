@echo off
REM ============================================================================
REM Financial Report Application - Production Deployment Script
REM ============================================================================
REM This batch file automates the complete production deployment process:
REM 1. Builds the React frontend application
REM 2. Sets up production environment variables
REM 3. Starts the Express server in production mode
REM 4. Displays application URLs and status information
REM ============================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Financial Report - Production Deployment
color 0A

REM Display header
echo.
echo ============================================================================
echo                    FINANCIAL REPORT APPLICATION
echo                      Production Deployment Script
echo ============================================================================
echo.

REM Check if we're in the correct directory (should contain package.json)
if not exist "package.json" (
    echo [ERROR] package.json not found!
    echo Please run this script from the Financial Report application root directory.
    echo.
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo [WARNING] node_modules directory not found!
    echo Installing dependencies...
    echo.
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies!
        pause
        exit /b 1
    )
)

REM Check if server directory exists
if not exist "server" (
    echo [ERROR] Server directory not found!
    echo Please ensure the server directory exists in the application root.
    echo.
    pause
    exit /b 1
)

REM Set production environment variable
echo [INFO] Setting environment to production mode...
set NODE_ENV=production
echo       NODE_ENV=%NODE_ENV%
echo.

REM Step 1: Build the React frontend application
echo ============================================================================
echo STEP 1: Building React Frontend Application
echo ============================================================================
echo [INFO] Starting frontend build process...
echo [INFO] This may take a few minutes depending on your system...
echo.

npm run build

REM Check if build was successful
if errorlevel 1 (
    echo.
    echo [ERROR] Frontend build failed!
    echo Please check the error messages above and fix any issues.
    echo Common issues:
    echo - Missing dependencies: Run 'npm install'
    echo - TypeScript/ESLint errors: Fix code issues
    echo - Memory issues: Close other applications
    echo.
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Frontend build completed successfully!
echo [INFO] Build files created in 'build' directory
echo.

REM Check if build directory was created
if not exist "build" (
    echo [ERROR] Build directory not found after build process!
    echo The build may have failed silently.
    echo.
    pause
    exit /b 1
)

REM Step 2: Navigate to server directory and check dependencies
echo ============================================================================
echo STEP 2: Preparing Server Environment
echo ============================================================================
echo [INFO] Navigating to server directory...

cd server

REM Check if we successfully changed to server directory
if not exist "package.json" (
    echo [ERROR] Server package.json not found!
    echo Please ensure the server directory contains a valid Node.js project.
    echo.
    pause
    exit /b 1
)

REM Check server dependencies
if not exist "node_modules" (
    echo [INFO] Installing server dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install server dependencies!
        pause
        exit /b 1
    )
)

echo [SUCCESS] Server environment prepared successfully!
echo.

REM Step 3: Display configuration information
echo ============================================================================
echo STEP 3: Configuration Information
echo ============================================================================

REM Read base path from server .env file if it exists
set BASE_PATH=/finreport
if exist ".env" (
    for /f "tokens=2 delims==" %%a in ('findstr "BASE_PATH" .env 2^>nul') do set BASE_PATH=%%a
)

REM Set default port
set PORT=5000
if exist ".env" (
    for /f "tokens=2 delims==" %%a in ('findstr "PORT" .env 2^>nul') do set PORT=%%a
)

echo [INFO] Application Configuration:
echo       Environment: %NODE_ENV%
echo       Port: %PORT%
echo       Base Path: %BASE_PATH%
echo       Build Directory: ../build
echo.

REM Step 4: Start the Express server
echo ============================================================================
echo STEP 4: Starting Express Server
echo ============================================================================
echo [INFO] Starting Financial Report server in production mode...
echo [INFO] Server will serve the built React application
echo.
echo [READY] Application URLs:
echo         Local:    http://localhost:%PORT%%BASE_PATH%
echo         Network:  http://[your-ip]:%PORT%%BASE_PATH%
echo.
echo [INFO] Press Ctrl+C to stop the server
echo [INFO] Server logs will appear below:
echo.
echo ============================================================================
echo                            SERVER OUTPUT
echo ============================================================================

REM Start the server with production environment
npm start

REM If we reach here, the server has stopped
echo.
echo ============================================================================
echo [INFO] Server has stopped.
echo ============================================================================
echo.

REM Return to original directory
cd ..

echo [INFO] Returned to application root directory
echo [INFO] Production deployment script completed
echo.
pause
