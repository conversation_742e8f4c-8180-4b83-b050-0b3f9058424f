import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import theme from './theme';
import AppLayout from './components/layout/AppLayout';
import Dashboard from './components/Dashboard';
import Salary from './components/Salary';
import Tally from './components/Tally';
import CostCenter from './components/CostCenter';
import ResourceUtilization from './components/ResourceUtilization';
import ResourceCost from './components/ResourceCost';
import EmployeeDiscrepancy from './components/EmployeeDiscrepancy';
import AboutApp from './components/AboutApp';
import { TourProvider } from './contexts/TourContext';
import './styles/tour.css';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <TourProvider>
          <AppLayout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/salary" element={<Salary />} />
              <Route path="/tally" element={<Tally />} />
              <Route path="/cost-center" element={<CostCenter />} />
              <Route path="/resource-utilization" element={<ResourceUtilization />} />
              <Route path="/resource-cost" element={<ResourceCost />} />
              <Route path="/employee-discrepancy" element={<EmployeeDiscrepancy />} />
              <Route path="/about" element={<AboutApp />} />
            </Routes>
          </AppLayout>
        </TourProvider>
      </Router>
    </ThemeProvider>
  );
}

export default App;