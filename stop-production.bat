@echo off
REM ============================================================================
REM Financial Report Application - Stop Production Server
REM ============================================================================
REM This batch file helps stop the production server and any related processes
REM ============================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Financial Report - Stop Production Server
color 0C

echo.
echo ============================================================================
echo                    FINANCIAL REPORT APPLICATION
echo                      Stop Production Server
echo ============================================================================
echo.

echo [INFO] Searching for Financial Report server processes...
echo.

REM Find and display Node.js processes that might be our server
echo [INFO] Current Node.js processes:
tasklist /fi "imagename eq node.exe" /fo table 2>nul

REM Check if any node processes are running
tasklist /fi "imagename eq node.exe" 2>nul | find /i "node.exe" >nul
if errorlevel 1 (
    echo [INFO] No Node.js processes found running.
    echo [INFO] The Financial Report server is not currently running.
    echo.
    pause
    exit /b 0
)

echo.
echo [WARNING] Found Node.js processes running.
echo.
echo Choose an option:
echo [1] Stop all Node.js processes (CAUTION: This will stop ALL Node.js applications)
echo [2] Stop processes running on port 5000 (Recommended)
echo [3] Manual process selection
echo [4] Cancel
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto STOP_ALL_NODE
if "%choice%"=="2" goto STOP_PORT_5000
if "%choice%"=="3" goto MANUAL_SELECTION
if "%choice%"=="4" goto CANCEL
goto INVALID_CHOICE

:STOP_ALL_NODE
echo.
echo [WARNING] This will stop ALL Node.js processes!
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto CANCEL

echo [INFO] Stopping all Node.js processes...
taskkill /f /im node.exe >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Failed to stop some processes. They may require administrator privileges.
) else (
    echo [SUCCESS] All Node.js processes stopped.
)
goto END

:STOP_PORT_5000
echo.
echo [INFO] Finding processes using port 5000...

REM Find process using port 5000
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5000"') do (
    set PID=%%a
    if defined PID (
        echo [INFO] Found process !PID! using port 5000
        taskkill /f /pid !PID! >nul 2>&1
        if errorlevel 1 (
            echo [ERROR] Failed to stop process !PID!. May require administrator privileges.
        ) else (
            echo [SUCCESS] Stopped process !PID!
        )
    )
)

REM Check if port 5000 is still in use
netstat -ano | findstr ":5000" >nul
if errorlevel 1 (
    echo [SUCCESS] Port 5000 is now free.
) else (
    echo [WARNING] Port 5000 may still be in use by other processes.
)
goto END

:MANUAL_SELECTION
echo.
echo [INFO] Current Node.js processes with details:
echo.

REM Display processes with PID for manual selection
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq node.exe" /fo csv ^| findstr /v "PID"') do (
    set PID=%%a
    set PID=!PID:"=!
    echo Process ID: !PID!
    
    REM Try to get command line (requires wmic)
    wmic process where "ProcessId=!PID!" get CommandLine /format:list 2>nul | findstr "CommandLine" 2>nul
    echo.
)

echo.
set /p target_pid="Enter the Process ID (PID) to stop (or 'cancel'): "

if /i "%target_pid%"=="cancel" goto CANCEL

REM Validate PID is numeric
echo %target_pid%| findstr /r "^[0-9][0-9]*$" >nul
if errorlevel 1 (
    echo [ERROR] Invalid PID. Please enter a numeric Process ID.
    pause
    goto MANUAL_SELECTION
)

echo [INFO] Stopping process %target_pid%...
taskkill /f /pid %target_pid% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Failed to stop process %target_pid%. It may not exist or require administrator privileges.
) else (
    echo [SUCCESS] Process %target_pid% stopped successfully.
)
goto END

:INVALID_CHOICE
echo [ERROR] Invalid choice. Please select 1, 2, 3, or 4.
pause
goto MANUAL_SELECTION

:CANCEL
echo [INFO] Operation cancelled.
goto END

:END
echo.
echo [INFO] Stop server operation completed.
echo.

REM Final check
tasklist /fi "imagename eq node.exe" 2>nul | find /i "node.exe" >nul
if errorlevel 1 (
    echo [SUCCESS] No Node.js processes are currently running.
) else (
    echo [INFO] Some Node.js processes are still running.
    echo [INFO] If the Financial Report server is still running, you may need to:
    echo         - Use administrator privileges
    echo         - Manually close the server terminal window
    echo         - Use Task Manager to end the process
)

echo.
pause
