import { Box, Typography, useTheme, useMediaQuery } from '@mui/material';

const Footer = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  return (
    <Box
      component="footer"
      sx={{
        width: '100%',
        padding: isMobile ? theme.spacing(2) : theme.spacing(2, 3),
        backgroundColor: theme.palette.grey[100],
        borderTop: `1px solid ${theme.palette.divider}`,
        mt: 'auto', // Push to bottom when content is short
        position: 'sticky',
        bottom: 0,
        zIndex: 10,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Typography 
        variant="body2" 
        color="text.secondary"
        sx={{ 
          fontSize: isMobile ? '0.75rem' : '0.875rem',
          fontWeight: 500,
        }}
      >
        Developed by <PERSON><PERSON><PERSON> B S
      </Typography>
    </Box>
  );
};

export default Footer;