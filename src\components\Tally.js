// src/components/Tally.js
import { useState, useEffect, useCallback, memo } from "react";
import {
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Button,
  CircularProgress,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  TextField,
  Grid,
  Divider,
} from "@mui/material";
import { Settings as SettingsIcon, Download as DownloadIcon } from "@mui/icons-material";
import { motion } from "framer-motion";
import * as XLSX from "xlsx";
import { loadCostCenterData, loadSalaryData, loadCostCenterMappings } from "../db";
import CostCenterMappingModal from "./CostCenterMappingModal";
import { applyCostCenterMapping, getCostCenterCategoryName, getCostCenterLedgerName, getUniqueCostCenters } from "../utils/costCenterMappings";

const Tally = ({ selectedMonth }) => {
  // Local state
  const [month, setMonth] = useState("");
  const [uniqueMonths, setUniqueMonths] = useState([]);
  const [aggregatedData, setAggregatedData] = useState([]);
  const [differenceData, setDifferenceData] = useState([]);
  const [totals, setTotals] = useState({ ccGross: 0, ccPF: 0, salaryGross: 0, salaryPF: 0 });
  const [loading, setLoading] = useState(false);

  // Cost center mapping state
  const [costCenterMappings, setCostCenterMappings] = useState({});
  const [mappingModalOpen, setMappingModalOpen] = useState(false);
  const [uniqueCostCenters, setUniqueCostCenters] = useState([]);

  // Report generation state
  const [reportData, setReportData] = useState([]);
  const [reportLoading, setReportLoading] = useState(false);
  const [pfReportData, setPFReportData] = useState([]);
  const [pfReportLoading, setPFReportLoading] = useState(false);
  const [manualFields, setManualFields] = useState({
    voucherDate: new Date().toISOString().split('T')[0],
    voucherTypeName: 'Journal',
    voucherNumber: '',
    voucherNarration: ''
  });

  // When parent passes a selectedMonth, update local state
  useEffect(() => {
    if (selectedMonth && uniqueMonths.includes(selectedMonth)) {
      setMonth(selectedMonth);
    }
  }, [selectedMonth, uniqueMonths]);

  // Memoized function to load cost center mappings
  const loadMappings = useCallback(async () => {
    try {
      const mappings = await loadCostCenterMappings();
      const mappingObject = {};
      mappings.forEach(mapping => {
        mappingObject[mapping.originalName] = {
          alternateName: mapping.alternateName,
          categoryName: mapping.categoryName || '',
          ledgerName: mapping.ledgerName || ''
        };
      });
      setCostCenterMappings(mappingObject);
    } catch (error) {
      console.error('Error loading cost center mappings:', error);
    }
  }, []);

  // Load cost center mappings
  useEffect(() => {
    loadMappings();

    // Listen for database changes to reload mappings
    const handleDBChange = () => loadMappings();
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [loadMappings]);

  // Memoized function to fetch unique months and cost centers
  const fetchUniqueMonths = useCallback(async () => {
    try {
      console.log('Tally: Loading cost center data...');
      const ccData = await loadCostCenterData();
      console.log(`Tally: Loaded ${ccData.length} cost center records`);

      if (ccData.length === 0) {
        console.warn('Tally: No cost center data found! This might be why only 4 cost centers are showing.');
        setUniqueCostCenters([]);
        setUniqueMonths([]);
        return;
      }

      const months = ccData
        .filter(record => record.month && record.month.trim() !== "")
        .map(record => record.month.trim());
      const unique = [...new Set(months)];
      setUniqueMonths(unique);
      console.log(`Tally: Found ${unique.length} unique months:`, unique);

      // Extract ALL unique cost centers from the entire dataset for the mapping modal
      console.log('Tally: Extracting unique cost centers...');
      console.log(`Tally: Total cost center records loaded: ${ccData.length}`);

      // Debug: Log first few records to see the data structure
      console.log('Tally: Sample cost center records:', ccData.slice(0, 5));

      // Debug: Check if costCenter field exists in the data
      const recordsWithCostCenter = ccData.filter(record => record.costCenter && record.costCenter.trim() !== '');
      console.log(`Tally: Records with valid cost center: ${recordsWithCostCenter.length}`);

      const allCostCenters = getUniqueCostCenters(ccData, 'costCenter');
      console.log(`Tally: Setting ${allCostCenters.length} unique cost centers for modal`);
      console.log('Tally: All unique cost centers:', allCostCenters);
      console.log('Tally: First 10 cost centers:', allCostCenters.slice(0, 10));
      console.log('Tally: Last 10 cost centers:', allCostCenters.slice(-10));

      if (allCostCenters.length === 0) {
        console.warn('Tally: No unique cost centers found! Check if the costCenter field exists in the data.');
      }

      setUniqueCostCenters(allCostCenters);

      // Clear the selected month if it no longer exists.
      if (month && !unique.includes(month)) {
        setMonth("");
      }
      // If no month is selected and there is at least one, default to the first
      if (!month && unique.length > 0) {
        setMonth(unique[0]);
      }
    } catch (error) {
      console.error('Tally: Error loading cost center data:', error);
      setUniqueCostCenters([]);
      setUniqueMonths([]);
    }
  }, [month]);

  // Fetch unique months from cost center data and update local state
  useEffect(() => {
    fetchUniqueMonths();
  }, [fetchUniqueMonths]); // Add fetchUniqueMonths as dependency

  // Memoized function to handle database changes
  const handleDBChange = useCallback(async () => {
    console.log('Tally: Database change detected, reloading cost center data...');
    try {
      const ccData = await loadCostCenterData();
      console.log(`Tally: Reloaded ${ccData.length} cost center records after DB change`);

      const allCostCenters = getUniqueCostCenters(ccData, 'costCenter');
      console.log(`Tally: Updated unique cost centers to ${allCostCenters.length} after DB change`);
      setUniqueCostCenters(allCostCenters);
    } catch (error) {
      console.error('Tally: Error reloading cost center data after DB change:', error);
    }
  }, []);

  // Listen for database changes to reload cost center data
  useEffect(() => {
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [handleDBChange]);

  // Memoized calculation function
  const calculateResourceCost = useCallback(async () => {
    if (!month) return;
    setLoading(true);
    try {
      const ccData = await loadCostCenterData();
      const salaryData = await loadSalaryData();

      // Filter cost center data for the selected month
      const filteredCC = ccData.filter(record => record.month && record.month.trim() === month);

      // Get unique cost centers for this month's calculation
      const costCentersSet = new Set();
      filteredCC.forEach(record => {
        if (record.costCenter) {
          costCentersSet.add(record.costCenter.trim());
        }
      });
      const costCenters = Array.from(costCentersSet);

      // Pre-calculate employee total hours to avoid repeated calculations
      const employeeHours = new Map();
      filteredCC.forEach(record => {
        const empName = record.name?.trim().toLowerCase();
        if (!empName) return;
        const hrs = parseFloat(record.hoursWorked || record.hours || 0);
        employeeHours.set(empName, (employeeHours.get(empName) || 0) + hrs);
      });

      // Pre-index salary data for faster lookup
      const salaryMap = new Map();
      salaryData.forEach(record => {
        if (record.payrollMonth && record.payrollMonth.trim() === month && record.name) {
          const empName = record.name.trim().toLowerCase();
          salaryMap.set(empName, {
            gross: parseFloat(record.gross || 0),
            pf: parseFloat(record.pf || 0)
          });
        }
      });

      // Calculate totals per cost center based on each employee's hours
      const ccTotals = {};
      filteredCC.forEach(record => {
        const cc = record.costCenter ? record.costCenter.trim() : "";
        if (!cc) return;

        const hrs = parseFloat(record.hoursWorked || record.hours || 0);
        if (hrs <= 0) return;

        const empName = record.name?.trim().toLowerCase();
        if (!empName) return;

        const totalHours = employeeHours.get(empName) || 0;
        const salary = salaryMap.get(empName);

        if (!salary || totalHours <= 0) return;

        // Compute cost for this record
        const cost = {
          gross: (salary.gross / totalHours) * hrs,
          pf: (salary.pf / totalHours) * hrs
        };

        if (!ccTotals[cc]) {
          ccTotals[cc] = { gross: 0, pf: 0 };
        }
        ccTotals[cc].gross += cost.gross;
        ccTotals[cc].pf += cost.pf;
      });

      // Calculate overall salary totals for the month
      const salaryTotals = Array.from(salaryMap.values()).reduce(
        (totals, salary) => ({
          gross: totals.gross + salary.gross,
          pf: totals.pf + salary.pf
        }),
        { gross: 0, pf: 0 }
      );

      // Prepare aggregated data array from costCenters and totals
      const aggregated = costCenters.map(cc => ({
        costCenter: applyCostCenterMapping(cc, costCenterMappings),
        originalCostCenter: cc,
        gross: ccTotals[cc] ? ccTotals[cc].gross : 0,
        pf: ccTotals[cc] ? ccTotals[cc].pf : 0,
      }));

      // Calculate differences for each cost center
      const differences = aggregated.map(item => ({
        ...item,
        grossDiff: salaryTotals.gross - item.gross,
        pfDiff: salaryTotals.pf - item.pf,
        salaryGross: salaryTotals.gross,
        salaryPF: salaryTotals.pf
      }));

      setAggregatedData(aggregated);
      setDifferenceData(differences);
      setTotals({
        ccGross: aggregated.reduce((sum, item) => sum + item.gross, 0),
        ccPF: aggregated.reduce((sum, item) => sum + item.pf, 0),
        salaryGross: salaryTotals.gross,
        salaryPF: salaryTotals.pf
      });
    } catch (error) {
      console.error("Error calculating resource cost:", error);
      setAggregatedData([]);
      setDifferenceData([]);
      setTotals({ ccGross: 0, ccPF: 0, salaryGross: 0, salaryPF: 0 });
    } finally {
      setLoading(false);
    }
  }, [month, costCenterMappings]);

  // Calculate tally data based on selected month
  useEffect(() => {
    calculateResourceCost();
  }, [calculateResourceCost]);

  // Function to format date from yyyy-mm-dd to dd-mm-yyyy
  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    const [year, month, day] = dateStr.split('-');
    return `${day}-${month}-${year}`;
  };

  // Generate report data for the selected month
  const generateReportData = useCallback(async () => {
    if (!month) {
      setReportData([]);
      return;
    }

    setReportLoading(true);
    try {
      // Use the existing aggregated data to generate report entries
      const reportEntries = aggregatedData.map((item, index) => {
        const categoryName = getCostCenterCategoryName(item.originalCostCenter, costCenterMappings);
        const ledgerName = getCostCenterLedgerName(item.originalCostCenter, costCenterMappings);
        // Use applyCostCenterMapping for the cost allocation column
        const mappedCostCenter = applyCostCenterMapping(item.originalCostCenter, costCenterMappings);

        return {
          id: index + 1,
          // Manual fields only appear in the first row, empty for subsequent rows
          voucherDate: index === 0 ? formatDate(manualFields.voucherDate) : '',
          voucherTypeName: index === 0 ? manualFields.voucherTypeName : '',
          voucherNumber: index === 0 ? manualFields.voucherNumber : '',
          voucherNarration: index === 0 ? manualFields.voucherNarration : '',
          // Auto-populated fields appear in all rows
          ledgerName: ledgerName || 'Staff Salaries- Indirect',
          ledgerAmount: item.gross || 0,
          ledgerAmountDrCr: 'Dr',
          categoryName: categoryName || item.costCenter,
          // Use mapped cost center name instead of original
          costAllocationCostCentre: mappedCostCenter,
          costAllocationAmount: item.gross || 0
        };
      });

      setReportData(reportEntries);
    } catch (error) {
      console.error("Error generating report data:", error);
      setReportData([]);
    } finally {
      setReportLoading(false);
    }
  }, [month, aggregatedData, costCenterMappings, manualFields]);
  
  // Generate PF report data for the selected month
  const generatePFReportData = useCallback(async () => {
    if (!month) {
      setPFReportData([]);
      return;
    }

    setPFReportLoading(true);
    try {
      // Use the existing aggregated data to generate report entries with PF instead of Gross
      const reportEntries = aggregatedData.map((item, index) => {
        const categoryName = getCostCenterCategoryName(item.originalCostCenter, costCenterMappings);
        const ledgerName = getCostCenterLedgerName(item.originalCostCenter, costCenterMappings);
        const mappedCostCenter = applyCostCenterMapping(item.originalCostCenter, costCenterMappings);

        return {
          id: index + 1,
          // Manual fields only appear in the first row, empty for subsequent rows
          voucherDate: index === 0 ? formatDate(manualFields.voucherDate) : '',
          voucherTypeName: index === 0 ? manualFields.voucherTypeName : '',
          voucherNumber: index === 0 ? manualFields.voucherNumber : '',
          voucherNarration: index === 0 ? manualFields.voucherNarration : '',
          // Auto-populated fields appear in all rows
          ledgerName: ledgerName || 'Staff PF- Indirect',
          ledgerAmount: item.pf || 0,
          ledgerAmountDrCr: 'Dr',
          categoryName: categoryName || item.costCenter,
          // Use mapped cost center name instead of original
          costAllocationCostCentre: mappedCostCenter,
          costAllocationAmount: item.pf || 0
        };
      });

      setPFReportData(reportEntries);
    } catch (error) {
      console.error("Error generating PF report data:", error);
      setPFReportData([]);
    } finally {
      setPFReportLoading(false);
    }
  }, [month, aggregatedData, costCenterMappings, manualFields]);

  // Generate report data when aggregated data changes
  useEffect(() => {
    generateReportData();
    generatePFReportData();
  }, [generateReportData, generatePFReportData]);

  // Enhanced export function with report format
  const handleExport = useCallback(() => {
    if (!month || aggregatedData.length === 0) return;

    const wb = XLSX.utils.book_new();
    // Include pfReportData in dependencies

    // First worksheet - Tally Report (matching the reference format)
    const reportHeaders = [
      "Voucher Date",
      "Voucher Type Name",
      "Voucher Number",
      "Ledger Name",
      "Ledger Amount",
      "Ledger Amount Dr/Cr",
      "Category Name",
      "Cost Allocation for - Cost Centre",
      "Cost Allocation for - Amount",
      "Voucher Narration"
    ];

    const reportRows = reportData.map(row => [
      row.voucherDate || '', // Already formatted in dd-mm-yyyy
      row.voucherTypeName || '', // Empty for subsequent rows
      row.voucherNumber || '', // Empty for subsequent rows
      row.ledgerName,
      (row.ledgerAmount || 0).toFixed(2),
      row.ledgerAmountDrCr,
      row.categoryName,
      row.costAllocationCostCentre, // Now contains mapped cost center names
      (row.costAllocationAmount || 0).toFixed(2),
      row.voucherNarration || '' // Empty for subsequent rows
    ]);

    const reportData_sheet = [reportHeaders, ...reportRows];
    const ws1 = XLSX.utils.aoa_to_sheet(reportData_sheet);

    // Set column widths for better readability
    ws1['!cols'] = [
      { wch: 12 }, // Voucher Date
      { wch: 18 }, // Voucher Type Name
      { wch: 15 }, // Voucher Number
      { wch: 25 }, // Ledger Name
      { wch: 15 }, // Ledger Amount
      { wch: 18 }, // Ledger Amount Dr/Cr
      { wch: 20 }, // Category Name
      { wch: 30 }, // Cost Allocation for - Cost Centre
      { wch: 20 }, // Cost Allocation for - Amount
      { wch: 30 }  // Voucher Narration
    ];

    XLSX.utils.book_append_sheet(wb, ws1, "Tally Report");
    
    // PF Report worksheet
    const pfReportRows = pfReportData.map(row => [
      row.voucherDate || '', // Already formatted in dd-mm-yyyy
      row.voucherTypeName || '',
      row.voucherNumber || '',
      row.ledgerName,
      (row.ledgerAmount || 0).toFixed(2),
      row.ledgerAmountDrCr,
      row.categoryName,
      row.costAllocationCostCentre,
      (row.costAllocationAmount || 0).toFixed(2),
      row.voucherNarration || ''
    ]);

    const pfReportData_sheet = [reportHeaders, ...pfReportRows];
    const ws4 = XLSX.utils.aoa_to_sheet(pfReportData_sheet);
    
    // Use the same column widths for consistency
    ws4['!cols'] = ws1['!cols'];
    
    XLSX.utils.book_append_sheet(wb, ws4, "PF Tally Report");

    // Second worksheet - Tally Data Summary
    const tallyData = [
      ["Sl No", "Cost Center", "Original Cost Center", "Gross Salary", "PF"],
      ...aggregatedData.map((row, idx) => [
        idx + 1,
        row.costCenter || "",
        row.originalCostCenter || row.costCenter || "",
        (row.gross || 0).toFixed(2),
        (row.pf || 0).toFixed(2),
      ]),
      [
        "Total",
        "",
        "",
        totals.ccGross.toFixed(2),
        totals.ccPF.toFixed(2),
      ],
    ];
    const ws2 = XLSX.utils.aoa_to_sheet(tallyData);
    XLSX.utils.book_append_sheet(wb, ws2, "Summary");

    // Third worksheet - Differences
    const differencesData = [
      [
        "Sl No",
        "Cost Center",
        "Original Cost Center",
        "Cost Center Gross",
        "Salary Gross",
        "Gross Difference",
        "Cost Center PF",
        "Salary PF",
        "PF Difference",
      ],
      ...differenceData.map((row, idx) => [
        idx + 1,
        row.costCenter || "",
        row.originalCostCenter || row.costCenter || "",
        (row.gross || 0).toFixed(2),
        (row.salaryGross || 0).toFixed(2),
        (row.grossDiff || 0).toFixed(2),
        (row.pf || 0).toFixed(2),
        (row.salaryPF || 0).toFixed(2),
        (row.pfDiff || 0).toFixed(2),
      ]),
      [
        "Total",
        "",
        "",
        totals.ccGross.toFixed(2),
        totals.salaryGross.toFixed(2),
        ((totals.ccGross || 0) - (totals.salaryGross || 0)).toFixed(2),
        totals.ccPF.toFixed(2),
        totals.salaryPF.toFixed(2),
        ((totals.ccPF || 0) - (totals.salaryPF || 0)).toFixed(2),
      ],
    ];
    const ws3 = XLSX.utils.aoa_to_sheet(differencesData);
    XLSX.utils.book_append_sheet(wb, ws3, "Differences");

    XLSX.writeFile(wb, `Tally_Report_${month}.xlsx`);
  }, [month, aggregatedData, differenceData, totals, reportData, pfReportData]);

  // Memoized modal close handler
  const handleCloseMappingModal = useCallback(() => {
    setMappingModalOpen(false);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Container sx={{ padding: { xs: 1, sm: 2, md: 3 } }}>
        <Card sx={{ mb: 3, overflow: 'visible' }}>
          <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 1 }}>
              <Typography variant="h5" component="h1" gutterBottom sx={{ mb: 0 }}>
                Tally Output
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title="Configure Cost Center Mappings">
                  <IconButton
                    onClick={() => setMappingModalOpen(true)}
                    color="primary"
                    size="small"
                  >
                    <SettingsIcon />
                  </IconButton>
                </Tooltip>
                <Button 
                  variant="contained" 
                  onClick={() => { setMonth(""); }}
                  size="small"
                >
                  Refresh Data
                </Button>
              </Box>
            </Box>

            <Box sx={{ width: "100%" }}>
              <FormControl fullWidth size="small">
                <InputLabel>Month</InputLabel>
                <Select
                  value={month}
                  onChange={(e) => setMonth(e.target.value)}
                  label="Month"
                >
                  {uniqueMonths.map((m) => (
                    <MenuItem key={m} value={m}>
                      {m}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </CardContent>
        </Card>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Card sx={{ mb: 3 }}>
              <CardContent sx={{ p: { xs: 1, sm: 2 } }}>
                <Typography variant="h6" sx={{ mb: 1, fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                  Aggregated Data (Cost Center Totals)
                </Typography>
                {aggregatedData.length === 0 ? (
                  <Typography>No aggregated data available for this month.</Typography>
                ) : (
                  <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell padding="checkbox">Sl No</TableCell>
                          <TableCell>Cost Center</TableCell>
                          <TableCell align="right">Gross Salary</TableCell>
                          <TableCell align="right">PF</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {aggregatedData.map((row, index) => (
                          <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                            <TableCell>{index + 1}</TableCell>
                            <TableCell>{row.costCenter || ""}</TableCell>
                            <TableCell>{(row.gross || 0).toFixed(2)}</TableCell>
                            <TableCell>{(row.pf || 0).toFixed(2)}</TableCell>
                          </TableRow>
                        ))}
                        <TableRow>
                          <TableCell colSpan={2} sx={{ fontWeight: "bold" }}>
                            Total
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {(totals.ccGross || 0).toFixed(2)}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {(totals.ccPF || 0).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </CardContent>
            </Card>

            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Salary vs Cost Center Comparison
                </Typography>
                <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Sl No</TableCell>
                        <TableCell>Cost Center</TableCell>
                        <TableCell>Cost Center Gross</TableCell>
                        <TableCell>Salary Gross</TableCell>
                        <TableCell>Gross Difference</TableCell>
                        <TableCell>Cost Center PF</TableCell>
                        <TableCell>Salary PF</TableCell>
                        <TableCell>PF Difference</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {differenceData.map((row, index) => (
                        <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{row.costCenter || ""}</TableCell>
                          <TableCell>{(row.gross || 0).toFixed(2)}</TableCell>
                          <TableCell>{(row.salaryGross || 0).toFixed(2)}</TableCell>
                          <TableCell sx={{ color: row.grossDiff !== 0 ? "red" : "inherit" }}>
                            {(row.grossDiff || 0).toFixed(2)}
                          </TableCell>
                          <TableCell>{(row.pf || 0).toFixed(2)}</TableCell>
                          <TableCell>{(row.salaryPF || 0).toFixed(2)}</TableCell>
                          <TableCell sx={{ color: row.pfDiff !== 0 ? "red" : "inherit" }}>
                            {(row.pfDiff || 0).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={2} sx={{ fontWeight: "bold" }}>
                          Total
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.ccGross || 0).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.salaryGross || 0).toFixed(2)}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: "bold",
                            color: (totals.ccGross - totals.salaryGross) !== 0 ? "red" : "inherit",
                          }}
                        >
                          {((totals.ccGross || 0) - (totals.salaryGross || 0)).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.ccPF || 0).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: "bold" }}>
                          {(totals.salaryPF || 0).toFixed(2)}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: "bold",
                            color: (totals.ccPF - totals.salaryPF) !== 0 ? "red" : "inherit",
                          }}
                        >
                          {((totals.ccPF || 0) - (totals.salaryPF || 0)).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            {/* Report Generation Section */}
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3 }}>
                  Report Generation
                </Typography>

                {/* Manual Input Fields */}
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Voucher Date"
                      type="date"
                      value={manualFields.voucherDate}
                      onChange={(e) => setManualFields(prev => ({ ...prev, voucherDate: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Voucher Type Name"
                      value={manualFields.voucherTypeName}
                      onChange={(e) => setManualFields(prev => ({ ...prev, voucherTypeName: e.target.value }))}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Voucher Number"
                      value={manualFields.voucherNumber}
                      onChange={(e) => setManualFields(prev => ({ ...prev, voucherNumber: e.target.value }))}
                      placeholder="Optional"
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Voucher Narration"
                      value={manualFields.voucherNarration}
                      onChange={(e) => setManualFields(prev => ({ ...prev, voucherNarration: e.target.value }))}
                      placeholder="Optional"
                    />
                  </Grid>
                </Grid>

                <Divider sx={{ my: 3 }} />

                {/* Gross Report Preview */}
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                  Gross Amount Report
                </Typography>
                {reportLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : reportData.length === 0 ? (
                  <Typography>No report data available for this month.</Typography>
                ) : (
                  <TableContainer 
                    component={Paper} 
                    sx={{ 
                      boxShadow: 'none', 
                      height: 400, 
                      mb: 4,
                      overflow: 'auto',
                      '& .MuiTableCell-root': { whiteSpace: 'nowrap' }
                    }}
                  >
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Voucher Date</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Voucher Type</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Voucher Number</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Ledger Name</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Ledger Amount</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Dr/Cr</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Category Name</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Cost Centre</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Cost Amount</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Narration</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {reportData.map((row, index) => (
                          <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                            <TableCell>{row.voucherDate || ''}</TableCell>
                            <TableCell>{row.voucherTypeName || ''}</TableCell>
                            <TableCell>{row.voucherNumber || ''}</TableCell>
                            <TableCell>{row.ledgerName}</TableCell>
                            <TableCell>{(row.ledgerAmount || 0).toFixed(2)}</TableCell>
                            <TableCell>{row.ledgerAmountDrCr}</TableCell>
                            <TableCell>{row.categoryName}</TableCell>
                            <TableCell>{row.costAllocationCostCentre}</TableCell>
                            <TableCell>{(row.costAllocationAmount || 0).toFixed(2)}</TableCell>
                            <TableCell>{row.voucherNarration || ''}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
                
                {/* PF Report Preview */}
                <Typography variant="subtitle1" sx={{ mb: 2, mt: 4, fontWeight: 'bold' }}>
                  PF Amount Report
                </Typography>
                {pfReportLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : pfReportData.length === 0 ? (
                  <Typography>No PF report data available for this month.</Typography>
                ) : (
                  <TableContainer 
                    component={Paper} 
                    sx={{ 
                      boxShadow: 'none', 
                      height: 400,
                      overflow: 'auto',
                      '& .MuiTableCell-root': { whiteSpace: 'nowrap' }
                    }}
                  >
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Voucher Date</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Voucher Type</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Voucher Number</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Ledger Name</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Ledger Amount</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Dr/Cr</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Category Name</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Cost Centre</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Cost Amount</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', color: 'black', position: 'sticky', top: 0, zIndex: 1 }}>Narration</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {pfReportData.map((row, index) => (
                          <TableRow key={index} sx={{ "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" } }}>
                            <TableCell>{row.voucherDate || ''}</TableCell>
                            <TableCell>{row.voucherTypeName || ''}</TableCell>
                            <TableCell>{row.voucherNumber || ''}</TableCell>
                            <TableCell>{row.ledgerName}</TableCell>
                            <TableCell>{(row.ledgerAmount || 0).toFixed(2)}</TableCell>
                            <TableCell>{row.ledgerAmountDrCr}</TableCell>
                            <TableCell>{row.categoryName}</TableCell>
                            <TableCell>{row.costAllocationCostCentre}</TableCell>
                            <TableCell>{(row.costAllocationAmount || 0).toFixed(2)}</TableCell>
                            <TableCell>{row.voucherNarration || ''}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </CardContent>
            </Card>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
              <Button
                variant="outlined"
                onClick={() => {
                  generateReportData();
                  generatePFReportData();
                }}
                disabled={!month || reportLoading || pfReportLoading}
                startIcon={(reportLoading || pfReportLoading) ? <CircularProgress size={20} /> : null}
              >
                Refresh Reports
              </Button>
              <Button
                variant="contained"
                onClick={handleExport}
                startIcon={<DownloadIcon />}
                sx={{ px: 3 }}
                disabled={!month || aggregatedData.length === 0}
              >
                Export as Excel
              </Button>
            </Box>
          </>
        )}

        {/* Cost Center Mapping Modal */}
        <CostCenterMappingModal
          open={mappingModalOpen}
          onClose={handleCloseMappingModal}
          costCenters={uniqueCostCenters}
        />
      </Container>
    </motion.div>
  );
};

export default memo(Tally);
