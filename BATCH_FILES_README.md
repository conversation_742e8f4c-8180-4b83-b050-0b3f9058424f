# Financial Report Application - Batch Files Documentation

This document provides comprehensive information about the Windows batch files created for automating the Financial Report application deployment and management processes.

## 📁 Available Batch Files

### 1. **finreport-manager.bat** (Master Console)
**Purpose**: Main entry point providing access to all deployment and management operations through an interactive menu.

**Features**:
- Interactive menu system
- Access to all deployment scripts
- Configuration management
- Development server options
- Utility functions

**Usage**:
```cmd
finreport-manager.bat
```

### 2. **start-production.bat** (Full Production Deployment)
**Purpose**: Complete production deployment workflow - builds frontend and starts server.

**Features**:
- Builds React frontend application
- Sets NODE_ENV to production
- Validates environment and dependencies
- Starts Express server in production mode
- Displays application URLs
- Comprehensive error handling

**Usage**:
```cmd
start-production.bat
```

**Process Flow**:
1. Environment validation
2. Frontend build (`npm run build`)
3. Server preparation
4. Production server start
5. URL display with custom base path

### 3. **restart-production.bat** (Quick Server Restart)
**Purpose**: Quickly restart the production server without rebuilding the frontend.

**Features**:
- Skips build process for faster restart
- Validates existing build
- Starts server with production configuration
- Ideal for server-only changes

**Usage**:
```cmd
restart-production.bat
```

### 4. **deploy-production.bat** (Advanced Deployment)
**Purpose**: Interactive deployment script with multiple options and advanced features.

**Features**:
- Interactive menu with deployment options
- Build backup creation
- Clean build functionality
- Health checks and validation
- Configuration display
- Selective deployment options

**Usage**:
```cmd
deploy-production.bat
```

**Menu Options**:
- Full Deployment (Build + Start)
- Quick Start (Server Only)
- Build Only
- Health Check
- View Configuration
- Clean Build

### 5. **stop-production.bat** (Server Management)
**Purpose**: Stop production server and manage Node.js processes.

**Features**:
- Multiple stop options
- Process identification
- Port-specific stopping (port 5000)
- Manual process selection
- Safety confirmations

**Usage**:
```cmd
stop-production.bat
```

## 🚀 Quick Start Guide

### For First-Time Deployment:
1. Run `finreport-manager.bat`
2. Choose option `[1] Start Production`
3. Wait for build and server start
4. Access application at displayed URL

### For Regular Use:
1. **Full Deployment**: `start-production.bat`
2. **Quick Restart**: `restart-production.bat`
3. **Stop Server**: `stop-production.bat`

## ⚙️ Configuration

### Environment Variables Set by Scripts:
- `NODE_ENV=production` - Sets production mode
- Reads `BASE_PATH` from `server/.env`
- Reads `PORT` from `server/.env`

### Default Configuration:
- **Port**: 5000
- **Base Path**: /finreport
- **Build Directory**: build/
- **Server Directory**: server/

### URLs Generated:
- **Production**: `http://localhost:5000/finreport`
- **Development**: `http://localhost:3000/finreport`

## 🔧 Prerequisites

### Required Software:
- **Node.js** (v14 or higher)
- **npm** (comes with Node.js)
- **Windows Command Prompt** or **PowerShell**

### Required Files:
- `package.json` (in root directory)
- `server/package.json` (server dependencies)
- Valid React application structure

## 📋 Error Handling

### Common Issues and Solutions:

#### 1. "package.json not found"
**Solution**: Run the batch file from the Financial Report application root directory.

#### 2. "Build failed"
**Solutions**:
- Run `npm install` to install dependencies
- Fix any TypeScript/ESLint errors
- Ensure sufficient system memory

#### 3. "Server dependencies failed"
**Solutions**:
- Navigate to `server/` directory
- Run `npm install` manually
- Check Node.js version compatibility

#### 4. "Port already in use"
**Solutions**:
- Use `stop-production.bat` to stop existing server
- Change port in `server/.env`
- Kill processes using Task Manager

## 🛠️ Customization

### Changing Default Port:
Edit `server/.env`:
```env
PORT=8080
```

### Changing Base Path:
Use the configuration script:
```cmd
node configure-url-path.js /myapp
```

Or edit environment files manually:
- `.env`: `REACT_APP_BASE_PATH=/myapp`
- `server/.env`: `BASE_PATH=/myapp`

### Adding Custom Scripts:
1. Create new `.bat` file
2. Add to `finreport-manager.bat` menu
3. Follow existing error handling patterns

## 📊 Script Features Comparison

| Feature | start-production | restart-production | deploy-production | stop-production |
|---------|------------------|-------------------|-------------------|-----------------|
| Frontend Build | ✅ | ❌ | ✅ (Optional) | ❌ |
| Server Start | ✅ | ✅ | ✅ (Optional) | ❌ |
| Interactive Menu | ❌ | ❌ | ✅ | ✅ |
| Error Handling | ✅ | ✅ | ✅ | ✅ |
| Health Checks | ✅ | ✅ | ✅ | ✅ |
| Process Management | ❌ | ❌ | ❌ | ✅ |

## 🔍 Troubleshooting

### Debug Mode:
Add `echo on` at the beginning of any batch file to see detailed execution steps.

### Log Files:
Server logs are displayed in the console. For persistent logging, consider redirecting output:
```cmd
start-production.bat > deployment.log 2>&1
```

### Manual Verification:
1. Check if `build/` directory exists after build
2. Verify server starts without errors
3. Test application URL in browser
4. Check process list for Node.js processes

## 📝 Best Practices

### Development Workflow:
1. Use `npm start` for development
2. Use `start-production.bat` for production testing
3. Use `restart-production.bat` for quick server restarts

### Production Deployment:
1. Always run full deployment first time
2. Use restart for server-only changes
3. Stop server properly before system shutdown

### Maintenance:
1. Regularly update dependencies
2. Clean build files periodically
3. Monitor server logs for errors

## 🆘 Support

### Getting Help:
1. Run `finreport-manager.bat` and choose `[H] Help`
2. Check `CUSTOM_URL_CONFIGURATION.md` for URL setup
3. Review application `README.md` for general information

### Reporting Issues:
Include the following information:
- Batch file used
- Error messages
- System configuration
- Node.js and npm versions

## 📄 File Structure

```
finreport/
├── finreport-manager.bat          # Master management console
├── start-production.bat           # Full production deployment
├── restart-production.bat         # Quick server restart
├── deploy-production.bat          # Advanced deployment options
├── stop-production.bat            # Server stop utility
├── configure-url-path.js          # URL configuration script
├── test-url-config.js             # Configuration test script
├── BATCH_FILES_README.md          # This documentation
└── CUSTOM_URL_CONFIGURATION.md    # URL configuration guide
```

This comprehensive batch file system provides a complete solution for managing the Financial Report application deployment lifecycle on Windows systems.
