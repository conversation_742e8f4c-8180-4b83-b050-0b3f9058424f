# Custom URL Path Configuration

This document explains how to configure the Financial Report application to run on a custom base URL path instead of the root path.

## Overview

The application has been configured to support custom base paths, allowing you to run it on URLs like:
- `http://localhost:3000/finreport` (development)
- `http://localhost:5000/finreport` (production)
- `https://yourdomain.com/finance` (production with custom domain)

## Configuration Files

### 1. Frontend Configuration (`.env`)

The main environment file controls the React application's base path:

```env
# Base path configuration for custom URL routing
# Set to empty string for root path, or "/finreport" for custom path
REACT_APP_BASE_PATH=/finreport
```

**Options:**
- `/finreport` - Application runs on `/finreport` path
- `/finance` - Application runs on `/finance` path
- `` (empty string) - Application runs on root path `/`

### 2. Server Configuration (`server/.env`)

The server environment file controls production static file serving:

```env
# Base path configuration for custom URL routing
# Set to empty string for root path, or "/finreport" for custom path
BASE_PATH=/finreport
```

### 3. Build Configuration (`package.json`)

The homepage field in package.json controls build-time path configuration:

```json
{
  "homepage": "/finreport"
}
```

### 4. PWA Configuration (`public/manifest.json`)

The start_url in manifest.json controls PWA launch URL:

```json
{
  "start_url": "/finreport"
}
```

## Development Environment Setup

### Option 1: Custom Path (e.g., `/finreport`)

1. Update `.env`:
   ```env
   REACT_APP_BASE_PATH=/finreport
   ```

2. Update `package.json`:
   ```json
   "homepage": "/finreport"
   ```

3. Update `public/manifest.json`:
   ```json
   "start_url": "/finreport"
   ```

4. Start development server:
   ```cmd
   npm start
   ```

5. Access application at: `http://localhost:3000/finreport`

### Option 2: Root Path (default behavior)

1. Update `.env`:
   ```env
   REACT_APP_BASE_PATH=
   ```

2. Update `package.json`:
   ```json
   "homepage": "."
   ```

3. Update `public/manifest.json`:
   ```json
   "start_url": "."
   ```

4. Access application at: `http://localhost:3000/`

## Production Environment Setup

### Option 1: Custom Path (e.g., `/finreport`)

1. Update `server/.env`:
   ```env
   BASE_PATH=/finreport
   NODE_ENV=production
   ```

2. Build the application:
   ```cmd
   npm run build
   ```

3. Start the server:
   ```cmd
   cd server
   npm start
   ```

4. Access application at: `http://localhost:5000/finreport`

### Option 2: Root Path

1. Update `server/.env`:
   ```env
   BASE_PATH=
   NODE_ENV=production
   ```

2. Update frontend `.env`:
   ```env
   REACT_APP_BASE_PATH=
   ```

3. Update `package.json`:
   ```json
   "homepage": "."
   ```

4. Build and start as above
5. Access application at: `http://localhost:5000/`

## Changing the Custom Path

To change from `/finreport` to a different path (e.g., `/finance`):

1. Update `.env`:
   ```env
   REACT_APP_BASE_PATH=/finance
   ```

2. Update `server/.env`:
   ```env
   BASE_PATH=/finance
   ```

3. Update `package.json`:
   ```json
   "homepage": "/finance"
   ```

4. Update `public/manifest.json`:
   ```json
   "start_url": "/finance"
   ```

5. Rebuild for production:
   ```cmd
   npm run build
   ```

## Deployment Considerations

### Nginx Configuration

If deploying behind Nginx, configure the location block:

```nginx
location /finreport {
    proxy_pass http://localhost:5000/finreport;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### Apache Configuration

For Apache with mod_proxy:

```apache
ProxyPass /finreport http://localhost:5000/finreport
ProxyPassReverse /finreport http://localhost:5000/finreport
```

## Troubleshooting

### Issue: 404 errors on page refresh
**Solution:** Ensure server catch-all route is configured correctly for the base path.

### Issue: Assets not loading
**Solution:** Verify `homepage` field in package.json matches the base path.

### Issue: API calls failing
**Solution:** Check that `REACT_APP_API_URL` in `.env` is correct for your deployment.

### Issue: Routing not working
**Solution:** Ensure `basename` prop is set correctly in React Router configuration.

## Testing the Configuration

1. **Development Test:**
   ```cmd
   npm start
   ```
   Navigate to `http://localhost:3000/finreport`

2. **Production Test:**
   ```cmd
   npm run build
   cd server
   npm start
   ```
   Navigate to `http://localhost:5000/finreport`

3. **Verify all routes work:**
   - Dashboard: `/finreport/`
   - Salary: `/finreport/salary`
   - Tally: `/finreport/tally`
   - Cost Center: `/finreport/cost-center`
   - etc.

## Notes

- The React development server automatically handles client-side routing
- In production, the Express server serves the React build and handles routing
- All internal navigation uses relative paths and will work with any base path
- External links and bookmarks will need to include the base path
