import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Zoom,
  useTheme,
  useMediaQuery
} from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloseIcon from '@mui/icons-material/Close';
import TourIcon from '@mui/icons-material/Tour';
import { useTour } from '../../contexts/TourContext';

const TourTooltip = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const {
    tourActive,
    tourStep,
    tourSteps,
    endTour,
    nextTourStep,
    previousTourStep,
    currentStep
  } = useTour();

  if (!tourActive || !currentStep) return null;

  const isFirstStep = tourStep === 0;
  const isLastStep = tourStep === tourSteps.length - 1;

  return (
    <>
      {/* Overlay to dim the background */}
      <Box
        className="tour-overlay"
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 9998,
          pointerEvents: 'none'
        }}
      />

      {/* Tour tooltip */}
      <Zoom in={tourActive}>
        <Paper
          className="tour-tooltip"
          elevation={6}
          sx={{
            position: 'fixed',
            bottom: isMobile ? 10 : 20,
            left: isMobile ? 10 : '50%',
            right: isMobile ? 10 : 'auto',
            transform: isMobile ? 'none' : 'translateX(-50%)',
            zIndex: 9999,
            p: 3,
            width: isMobile ? 'auto' : '500px',
            backgroundColor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
            borderRadius: 2,
            maxWidth: isMobile ? 'calc(100vw - 20px)' : '500px'
          }}
        >
          {/* Header with title and step indicator */}
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Box display="flex" alignItems="center" gap={1}>
              {isFirstStep && <TourIcon />}
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                {currentStep.title}
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={2}>
              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                {tourStep + 1} of {tourSteps.length}
              </Typography>
              <Button
                size="small"
                onClick={endTour}
                sx={{ minWidth: 'auto', p: 0.5, color: 'inherit' }}
              >
                <CloseIcon fontSize="small" />
              </Button>
            </Box>
          </Box>

          {/* Description */}
          <Typography variant="body1" paragraph sx={{ mb: 3 }}>
            {currentStep.description}
          </Typography>

          {/* Step indicators */}
          <Box className="tour-step-indicator" sx={{ mb: 3 }}>
            {tourSteps.map((_, index) => (
              <Box
                key={index}
                className={`tour-step-dot ${index === tourStep ? 'active' : ''}`}
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: index === tourStep 
                    ? 'rgba(255, 255, 255, 0.8)' 
                    : 'rgba(255, 255, 255, 0.3)',
                  transition: 'background-color 0.3s ease'
                }}
              />
            ))}
          </Box>

          {/* Navigation buttons */}
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Button
              variant="outlined"
              color="inherit"
              onClick={endTour}
              size="small"
              className="tour-nav-button"
            >
              Exit Tour
            </Button>

            <Box display="flex" gap={1}>
              {!isFirstStep && (
                <Button
                  variant="text"
                  color="inherit"
                  onClick={previousTourStep}
                  size="small"
                  startIcon={<ArrowBackIcon />}
                  className="tour-nav-button"
                >
                  Previous
                </Button>
              )}
              <Button
                variant="contained"
                color="secondary"
                endIcon={isLastStep ? null : <ArrowForwardIcon />}
                onClick={nextTourStep}
                size="small"
                className="tour-nav-button"
              >
                {isLastStep ? 'Finish Tour' : 'Next'}
              </Button>
            </Box>
          </Box>
        </Paper>
      </Zoom>
    </>
  );
};

export default TourTooltip;
